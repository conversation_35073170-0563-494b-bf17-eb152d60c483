# Microservice Template Repository

A comprehensive, production-ready microservice template featuring modern architecture patterns, AWS infrastructure, and best practices for scalable applications.

## 🏗️ Architecture Overview

This template implements a **Backend-For-Frontend (BFF)** pattern with micro-frontend architecture, designed for AWS deployment with infrastructure-as-code principles.

### Key Features

- **🚀 Modern Tech Stack**: React 19, Node.js, TypeScript, Prisma ORM
- **☁️ AWS Native**: Amplify hosting, RDS PostgreSQL, CloudWatch monitoring
- **🔐 Enterprise Auth**: Auth0 integration with JWT validation and RBAC
- **🏗️ Infrastructure as Code**: Terraform configurations for all AWS resources
- **🧪 Testing Strategy**: Comprehensive testing setup for all components
- **📦 Containerized Development**: Docker Compose for local development
- **🔄 CI/CD Ready**: Amplify pipeline with automated testing and deployment

## 📁 Repository Structure

```
├── backend/                 # BFF API Layer (Express.js + Prisma)
│   ├── src/
│   ├── prisma/
│   ├── tests/
│   └── Dockerfile
├── frontend/               # Micro-frontend (React 19 + Material UI)
│   ├── src/
│   ├── public/
│   ├── tests/
│   └── Dockerfile
├── infrastructure/         # Terraform configurations
│   ├── modules/
│   ├── environments/
│   └── scripts/
├── docker/                # Docker Compose configurations
├── docs/                  # Architecture diagrams and documentation
├── scripts/               # Development and deployment scripts
└── .github/               # GitHub Actions workflows
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm/yarn
- Docker and Docker Compose
- Terraform CLI
- AWS CLI configured
- Auth0 account

### Local Development Setup

1. **Clone and install dependencies**:
```bash
git clone <repository-url>
cd test-microservice-template
npm run install:all
```

2. **Setup environment variables**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start local development environment**:
```bash
docker-compose up -d postgres redis
npm run dev
```

4. **Run database migrations**:
```bash
npm run db:migrate
npm run db:seed
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## 🏗️ Architecture Patterns

### Backend-For-Frontend (BFF)
The backend serves as a dedicated API layer for the frontend, providing:
- Data aggregation from multiple sources
- Authentication and authorization
- Business logic specific to frontend needs
- API versioning and backward compatibility

### Micro-Frontend Architecture
- **Module Federation**: Independent deployment of frontend modules
- **Shared Dependencies**: Optimized bundle sizes with shared libraries
- **Independent Teams**: Each micro-frontend can be developed independently

### Infrastructure as Code
- **Terraform**: All AWS resources defined as code
- **Environment Parity**: Consistent infrastructure across environments
- **Version Control**: Infrastructure changes tracked in Git

## 📚 Documentation

- [Architecture Guide](./docs/architecture.md)
- [Development Workflow](./docs/development.md)
- [Deployment Guide](./docs/deployment.md)
- [API Documentation](./docs/api.md)
- [Testing Strategy](./docs/testing.md)

## 🧪 Testing

```bash
# Run all tests
npm run test

# Run specific test suites
npm run test:backend
npm run test:frontend
npm run test:e2e

# Run tests with coverage
npm run test:coverage
```

## 🚀 Deployment

### Development Environment
```bash
npm run deploy:dev
```

### Production Environment
```bash
npm run deploy:prod
```

## 🤝 Contributing

Please read our [Contributing Guide](./CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.

## 🆘 Support

For support and questions, please refer to our [documentation](./docs/) or create an issue in this repository.
