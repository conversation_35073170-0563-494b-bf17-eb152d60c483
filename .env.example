# Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NODE_ENV=development
PORT=8000
FRONTEND_PORT=3000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Local Development (Docker)
DATABASE_URL="postgresql://postgres:password@localhost:5432/microservice_db?schema=public"

# Production (AWS RDS)
# DATABASE_URL="*****************************************************/microservice_db?schema=public"

# =============================================================================
# AUTH0 CONFIGURATION
# =============================================================================
AUTH0_DOMAIN=your-auth0-domain.auth0.com
AUTH0_CLIENT_ID=your-auth0-client-id
AUTH0_CLIENT_SECRET=your-auth0-client-secret
AUTH0_AUDIENCE=your-auth0-api-identifier
AUTH0_CALLBACK_URL=http://localhost:3000/callback
AUTH0_LOGOUT_URL=http://localhost:3000

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# =============================================================================
# AWS CONFIGURATION
# =============================================================================
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# S3 Configuration
S3_BUCKET_NAME=your-s3-bucket-name
S3_REGION=us-east-1

# CloudWatch Configuration
CLOUDWATCH_LOG_GROUP=/aws/lambda/microservice-template
CLOUDWATCH_LOG_STREAM=backend-api

# =============================================================================
# REDIS CONFIGURATION (Optional - for caching)
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# =============================================================================
# API CONFIGURATION
# =============================================================================
API_BASE_URL=http://localhost:8000
API_VERSION=v1
API_RATE_LIMIT=100
API_RATE_LIMIT_WINDOW=15

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=debug
LOG_FORMAT=combined

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
# Application Performance Monitoring
APM_SERVICE_NAME=microservice-template
APM_ENVIRONMENT=development

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# =============================================================================
# FEATURE FLAGS
# =============================================================================
FEATURE_FLAG_NEW_UI=true
FEATURE_FLAG_ANALYTICS=false
FEATURE_FLAG_BETA_FEATURES=false

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Email Service (e.g., SendGrid, SES)
EMAIL_SERVICE_API_KEY=your-email-service-api-key
EMAIL_FROM_ADDRESS=<EMAIL>

# Analytics Service
ANALYTICS_API_KEY=your-analytics-api-key

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
# Enable/disable development features
ENABLE_SWAGGER_UI=true
ENABLE_GRAPHQL_PLAYGROUND=true
ENABLE_DEBUG_LOGGING=true

# Hot reload and development server settings
CHOKIDAR_USEPOLLING=false
FAST_REFRESH=true
