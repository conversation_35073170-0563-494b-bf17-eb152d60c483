{"name": "microservice-backend", "version": "1.0.0", "description": "Backend-For-Frontend API with Express.js, Prisma, and Auth0", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc && tsc-alias", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "jest --config jest.e2e.config.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:seed": "tsx src/prisma/seed.ts", "db:reset": "prisma migrate reset --force", "db:studio": "prisma studio", "db:push": "prisma db push", "clean": "<PERSON><PERSON><PERSON> dist", "typecheck": "tsc --noEmit", "docs:generate": "swagger-jsdoc -d swaggerDef.js src/routes/*.ts -o docs/swagger.json"}, "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "dotenv": "^16.3.1", "joi": "^17.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-jwt": "^8.4.1", "jwks-rsa": "^3.1.0", "auth0": "^4.1.0", "@prisma/client": "^5.7.1", "redis": "^4.6.11", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "express-openapi-validator": "^5.1.2", "http-status-codes": "^2.3.0", "uuid": "^9.0.1", "multer": "^1.4.5-lts.1", "aws-sdk": "^2.1506.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^9.0.7", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "typescript": "^5.3.2", "tsx": "^4.6.2", "tsc-alias": "^1.8.8", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "prisma": "^5.7.1", "rimraf": "^5.0.5"}, "prisma": {"seed": "tsx src/prisma/seed.ts"}, "engines": {"node": ">=18.0.0"}}