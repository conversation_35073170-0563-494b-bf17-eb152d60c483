// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model with Auth0 integration
model User {
  id          String   @id @default(cuid())
  auth0Id     String   @unique @map("auth0_id")
  email       String   @unique
  name        String?
  picture     String?
  role        Role     @default(USER)
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  lastLoginAt DateTime? @map("last_login_at")

  // Relations
  posts    Post[]
  comments Comment[]
  sessions Session[]

  @@map("users")
}

// Role enumeration
enum Role {
  USER
  ADMIN
  MODERATOR
}

// Session model for tracking user sessions
model Session {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  token     String   @unique
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")
  isActive  Boolean  @default(true) @map("is_active")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// Post model for content management
model Post {
  id          String      @id @default(cuid())
  title       String
  content     String?
  excerpt     String?
  slug        String      @unique
  status      PostStatus  @default(DRAFT)
  authorId    String      @map("author_id")
  publishedAt DateTime?   @map("published_at")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  // Relations
  author   User      @relation(fields: [authorId], references: [id], onDelete: Cascade)
  comments Comment[]
  tags     PostTag[]

  @@map("posts")
}

// Post status enumeration
enum PostStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

// Comment model
model Comment {
  id        String   @id @default(cuid())
  content   String
  postId    String   @map("post_id")
  authorId  String   @map("author_id")
  parentId  String?  @map("parent_id")
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  post     Post      @relation(fields: [postId], references: [id], onDelete: Cascade)
  author   User      @relation(fields: [authorId], references: [id], onDelete: Cascade)
  parent   Comment?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies  Comment[] @relation("CommentReplies")

  @@map("comments")
}

// Tag model
model Tag {
  id          String    @id @default(cuid())
  name        String    @unique
  slug        String    @unique
  description String?
  color       String?
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // Relations
  posts PostTag[]

  @@map("tags")
}

// Many-to-many relation between Post and Tag
model PostTag {
  postId String @map("post_id")
  tagId  String @map("tag_id")

  // Relations
  post Post @relation(fields: [postId], references: [id], onDelete: Cascade)
  tag  Tag  @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([postId, tagId])
  @@map("post_tags")
}

// Audit log model for tracking changes
model AuditLog {
  id        String   @id @default(cuid())
  userId    String?  @map("user_id")
  action    String
  resource  String
  resourceId String? @map("resource_id")
  oldValues Json?    @map("old_values")
  newValues Json?    @map("new_values")
  ipAddress String?  @map("ip_address")
  userAgent String?  @map("user_agent")
  createdAt DateTime @default(now()) @map("created_at")

  @@map("audit_logs")
}

// Configuration model for application settings
model Configuration {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  type      String   @default("string")
  isPublic  Boolean  @default(false) @map("is_public")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("configurations")
}
