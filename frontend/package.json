{"name": "microservice-frontend", "version": "1.0.0", "description": "React 19 micro-frontend with Material UI and Module Federation", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,js,jsx}", "typecheck": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@auth0/auth0-react": "^2.2.4", "swr": "^2.2.4", "axios": "^1.6.2", "@tanstack/react-query": "^5.12.2", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "lodash": "^4.17.21", "react-helmet-async": "^2.0.4"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/lodash": "^4.14.202", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/ui": "^1.0.4", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^23.0.1", "prettier": "^3.1.1", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4", "rimraf": "^5.0.5", "@originjs/vite-plugin-federation": "^1.3.5"}, "engines": {"node": ">=18.0.0"}}