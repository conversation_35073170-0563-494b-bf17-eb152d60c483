import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import federation from '@originjs/vite-plugin-federation';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    federation({
      name: 'microservice-frontend',
      filename: 'remoteEntry.js',
      // Modules to expose for other micro-frontends
      exposes: {
        './App': './src/App.tsx',
        './AuthProvider': './src/providers/AuthProvider.tsx',
        './ApiProvider': './src/providers/ApiProvider.tsx',
        './ThemeProvider': './src/providers/ThemeProvider.tsx',
        './UserDashboard': './src/components/UserDashboard.tsx',
        './PostList': './src/components/PostList.tsx',
      },
      // Shared dependencies
      shared: {
        react: {
          singleton: true,
          requiredVersion: '^18.2.0',
        },
        'react-dom': {
          singleton: true,
          requiredVersion: '^18.2.0',
        },
        '@mui/material': {
          singleton: true,
          requiredVersion: '^5.15.0',
        },
        '@emotion/react': {
          singleton: true,
          requiredVersion: '^11.11.1',
        },
        '@emotion/styled': {
          singleton: true,
          requiredVersion: '^11.11.0',
        },
        'react-router-dom': {
          singleton: true,
          requiredVersion: '^6.20.1',
        },
        '@auth0/auth0-react': {
          singleton: true,
          requiredVersion: '^2.2.4',
        },
      },
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    host: true,
    cors: true,
  },
  preview: {
    port: 3000,
    host: true,
  },
  build: {
    target: 'esnext',
    minify: false,
    cssCodeSplit: false,
    rollupOptions: {
      external: [],
    },
  },
  define: {
    global: 'globalThis',
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
  },
});
