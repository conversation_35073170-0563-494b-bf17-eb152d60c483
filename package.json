{"name": "microservice-template", "version": "1.0.0", "description": "Comprehensive microservice template with AWS infrastructure, BFF pattern, and micro-frontend architecture", "private": true, "workspaces": ["backend", "frontend"], "scripts": {"install:all": "npm install && npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm run test", "test:frontend": "cd frontend && npm run test", "test:e2e": "cd tests/e2e && npm run test", "test:coverage": "npm run test:backend -- --coverage && npm run test:frontend -- --coverage", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "npm run lint:backend -- --fix && npm run lint:frontend -- --fix", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:reset": "cd backend && npm run db:reset", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "infra:plan": "cd infrastructure && terraform plan", "infra:apply": "cd infrastructure && terraform apply", "infra:destroy": "cd infrastructure && terraform destroy", "deploy:dev": "npm run build && npm run infra:apply", "deploy:prod": "npm run build && cd infrastructure && terraform workspace select prod && terraform apply", "docs:serve": "cd docs && npx http-server -p 8080", "clean": "npm run clean:backend && npm run clean:frontend", "clean:backend": "cd backend && npm run clean", "clean:frontend": "cd frontend && npm run clean"}, "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/microservice-template.git"}, "keywords": ["microservice", "template", "aws", "amplify", "react", "nodejs", "typescript", "terraform", "bff", "micro-frontend"], "author": "Your Organization", "license": "MIT"}